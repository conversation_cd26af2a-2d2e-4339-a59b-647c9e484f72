{"overall_precision": 0.6626016260162602, "overall_recall": 0.635890767230169, "overall_f1_score": 0.6489714664897147, "temporal_stability": 0.6, "consistent_sequences_ratio": 0.6, "total_sequences": 5, "consistent_sequences": 3, "sequence_results": {"data03": {"precision": 0.08333333333333333, "recall": 0.125, "f1_score": 0.1, "temporal_consistency": 0.08333333333333333, "total_frames": 12, "matched_frames": 1, "total_tp": 1, "total_fp": 11, "total_fn": 7}, "data05": {"precision": 0.2553191489361702, "recall": 0.1518987341772152, "f1_score": 0.1904761904761905, "temporal_consistency": 0.1518987341772152, "total_frames": 79, "matched_frames": 12, "total_tp": 12, "total_fp": 35, "total_fn": 67}, "data20": {"precision": 0.5206896551724138, "recall": 0.515358361774744, "f1_score": 0.5180102915951972, "temporal_consistency": 0.515358361774744, "total_frames": 293, "matched_frames": 151, "total_tp": 151, "total_fp": 139, "total_fn": 142}, "data21": {"precision": 0.885, "recall": 0.885, "f1_score": 0.885, "temporal_consistency": 0.885, "total_frames": 200, "matched_frames": 177, "total_tp": 177, "total_fp": 23, "total_fn": 23}, "data22": {"precision": 0.783068783068783, "recall": 0.783068783068783, "f1_score": 0.7830687830687831, "temporal_consistency": 0.783068783068783, "total_frames": 189, "matched_frames": 148, "total_tp": 148, "total_fp": 41, "total_fn": 41}}}