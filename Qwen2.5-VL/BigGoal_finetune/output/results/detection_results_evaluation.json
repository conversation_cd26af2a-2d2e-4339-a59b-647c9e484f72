{"overall_precision": 0.3076459963877182, "overall_recall": 0.3732651570489408, "overall_f1_score": 0.33729372937293733, "temporal_stability": 0.14285714285714285, "consistent_sequences_ratio": 0.14285714285714285, "total_sequences": 7, "consistent_sequences": 1, "sequence_results": {"data03": {"precision": 0.08333333333333333, "recall": 0.125, "f1_score": 0.1, "temporal_consistency": 0.08333333333333333, "total_frames": 12, "matched_frames": 1, "total_tp": 1, "total_fp": 11, "total_fn": 7}, "data05": {"precision": 0.2553191489361702, "recall": 0.1518987341772152, "f1_score": 0.1904761904761905, "temporal_consistency": 0.1518987341772152, "total_frames": 79, "matched_frames": 12, "total_tp": 12, "total_fp": 35, "total_fn": 67}, "data20": {"precision": 0.5206896551724138, "recall": 0.515358361774744, "f1_score": 0.5180102915951972, "temporal_consistency": 0.515358361774744, "total_frames": 293, "matched_frames": 151, "total_tp": 151, "total_fp": 139, "total_fn": 142}, "data21": {"precision": 0.885, "recall": 0.885, "f1_score": 0.885, "temporal_consistency": 0.885, "total_frames": 200, "matched_frames": 177, "total_tp": 177, "total_fp": 23, "total_fn": 23}, "data22": {"precision": 0.783068783068783, "recall": 0.783068783068783, "f1_score": 0.7830687830687831, "temporal_consistency": 0.783068783068783, "total_frames": 189, "matched_frames": 148, "total_tp": 148, "total_fp": 41, "total_fn": 41}, "data24": {"precision": 0.04081632653061224, "recall": 0.006666666666666667, "f1_score": 0.011461318051575931, "temporal_consistency": 0.006666666666666667, "total_frames": 300, "matched_frames": 2, "total_tp": 2, "total_fp": 47, "total_fn": 298}, "data25": {"precision": 0.02288329519450801, "recall": 0.06666666666666667, "f1_score": 0.034071550255536626, "temporal_consistency": 0.06666666666666667, "total_frames": 300, "matched_frames": 20, "total_tp": 20, "total_fp": 854, "total_fn": 280}}}